# Настройки для подключения к LiteLLM API
LITELLM_API_BASE=http://localhost:4000
LITELLM_MASTER_KEY=sk-1234
LITELLM_SALT_KEY=sk-1234
LITELLM_LOG=DEBUG

# Настройки для GPUStack
GPUSTACK_URL=http://localhost:80
GPUSTACK_INTERNAL=http://gpustack-server:80

GPUSTACK_TOKEN='17c49b3f13311cde2db13b2ccd0b6d53'
GPUSTACK_PASSWORD='Bqeh#xjBc1iY'
GPUSTACK_KEY='gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa'

# Настройки для сервиса предиктора времени ответа
PREDICT_URL=http://localhost:8007
PREDICT_URL_INTERNAL=http://filin-predictor:8007
PREDICT_TIMEOUT=5

# Настройки базы данных
DATABASE_URL="********************************************/litellm"
DATABASE_URL_HOST="postgresql://llmproxy:dbpassword9090@localhost:5432/litellm"

# Настройки миграции litellm
# DISABLE_SCHEMA_UPDATE=True

# Параметры миграции аналитики
USE_ANALYTICS_MIGRATE=True
ANALYTICS_MIGRATION_DIR=
DISABLE_ANALYTICS_SCHEMA_UPDATE=
ANALYTICS_ENABLED=true

# Настройки метрик
LLM_ROUTER_PROMETHEUS_METRICS=True
LLM_ROUTER_SYSTEM_METRICS=True
LLM_ROUTER_END_USER_TRACKING=False
LLM_ROUTER_METRICS_DEBUG=False

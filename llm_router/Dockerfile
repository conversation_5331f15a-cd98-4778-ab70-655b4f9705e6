# Base image for building
ARG LITELLM_BUILD_IMAGE=cgr.dev/chainguard/python:latest-dev

# Runtime image
ARG LITELLM_RUNTIME_IMAGE=cgr.dev/chainguard/python:latest-dev

# Builder stage
FROM $LITELLM_BUILD_IMAGE AS builder

# Set the working directory to /app
WORKDIR /app

USER root

# Install build dependencies
RUN apk add --no-cache gcc python3-dev openssl openssl-dev

RUN pip install --upgrade pip && \
    pip install build

# Copy only necessary files for building (llm_router/ excluded via .dockerignore)
COPY . .

# Build Admin UI (only once)
RUN chmod +x docker/build_admin_ui.sh && ./docker/build_admin_ui.sh

# Build the package
RUN rm -rf dist/* && python -m build

# There should be only one wheel file now, assume the build only creates one
RUN ls -1 dist/*.whl | head -1

# Install the package
RUN pip install dist/*.whl

# Install dependencies as wheels
RUN pip wheel --no-cache-dir --wheel-dir=/wheels/ -r requirements.txt

# Ensure pyjwt is used, not jwt
RUN pip uninstall jwt -y
RUN pip uninstall PyJWT -y
RUN pip install PyJWT==2.9.0 --no-cache-dir

# Runtime stage
FROM $LITELLM_RUNTIME_IMAGE AS runtime

# Ensure runtime stage runs as root
USER root

# Install runtime dependencies
RUN apk add --no-cache openssl postgresql-client

WORKDIR /app

# Copy base LiteLLM files (llm_router/ excluded via .dockerignore)
COPY . .
RUN ls -la /app

# Copy the built wheel from the builder stage to the runtime stage
COPY --from=builder /app/dist/*.whl .
COPY --from=builder /wheels/ /wheels/

# Install the built wheel using pip
RUN pip install *.whl /wheels/* --no-index --find-links=/wheels/ && rm -f *.whl && rm -rf /wheels

# Install HTTP client for predictor service and testing dependencies
RUN pip install requests pytest pytest-asyncio tabulate

# Install analytics dependencies for database and health monitoring
RUN pip install asyncpg psycopg2-binary pandas

RUN chmod +x docker/entrypoint.sh
RUN chmod +x docker/prod_entrypoint.sh

EXPOSE 4000/tcp

# Create directories for license module
RUN mkdir -p /app/litellm/proxy/auth/
RUN mkdir -p /usr/local/lib/python3.13/site-packages/litellm/proxy/auth/

# Copy llm_router files (this layer will be rebuilt when llm_router/ changes)
COPY llm_router/ /app/llm_router/

# Append analytics schema to main schema.prisma to create complete schema (skip datasource and generator)
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/schema.prisma
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/litellm-proxy-extras/litellm_proxy_extras/schema.prisma

# Generate prisma clients for merged schema
RUN prisma generate

# Copy license.py to specific locations for LiteLLM proxy auth
COPY llm_router/license.py /usr/local/lib/python3.13/site-packages/litellm/proxy/auth/litellm_license.py
COPY llm_router/license.py /usr/lib/python3.13/site-packages/litellm/proxy/auth/litellm_license.py
COPY llm_router/license.py /app/litellm/proxy/auth/litellm_license.py
COPY llm_router/analytics/migrations/ /app/litellm-proxy-extras/litellm_proxy_extras/migrations/

# Set permissions for llm_router entrypoint
RUN chmod +x /app/llm_router/entrypoint.sh

# Set environment variable for config
#ENV LITELLM_CONFIG_PATH="/app/llm_router/litellm.config.yaml"
#ENV CONFIG_FILE_PATH="/app/llm_router/litellm.config.yaml"

# Set entrypoint and command
ENTRYPOINT ["/app/llm_router/entrypoint.sh"]
CMD ["python", "/app/llm_router/start_litellm_with_router.py", "--port", "4000"]
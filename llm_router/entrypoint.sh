#!/bin/bash
echo $(pwd)

# Run the LiteLLM migration script
echo "Running LiteLLM migrations..."
python3 litellm/proxy/prisma_migration.py

# Disable schema updates during server startup to avoid double migration checks
export DISABLE_SCHEMA_UPDATE=true

# Check if the LiteLLM migration script executed successfully
if [ $? -eq 0 ]; then
    echo "LiteLLM migration script ran successfully!"
else
    echo "LiteLLM migration script failed!"
    exit 1
fi

# Run the Analytics migration script as module
# echo "Running Analytics migrations..."
# python3 -m llm_router.analytics_migration

# Check if the Analytics migration script executed successfully
# if [ $? -eq 0 ]; then
#     echo "Analytics migration script ran successfully!"
# else
#     echo "Analytics migration script failed!"
#     # Не завершаем с ошибкой, так как Analytics не критична для основной функциональности
#     echo "Warning: Analytics migrations failed, but continuing startup..."
# fi

echo "Starting LiteLLM with custom ML router..."
exec "$@"
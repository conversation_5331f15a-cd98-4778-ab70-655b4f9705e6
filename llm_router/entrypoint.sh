#!/bin/bash
echo $(pwd)

# Add analytics tables to both schema files (skip first 8 lines with datasource/generator)
echo "Adding analytics schema to LiteLLM schemas..."
tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/schema.prisma
tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/litellm-proxy-extras/litellm_proxy_extras/schema.prisma

# Copy analytics migrations to LiteLLM migrations directory
echo "Copying analytics migrations..."
cp -r /app/llm_router/analytics/migrations/* /app/litellm-proxy-extras/litellm_proxy_extras/migrations/

# Run the LiteLLM migration script
echo "Running LiteLLM migrations..."
python3 litellm/proxy/prisma_migration.py

# Check if the LiteLLM migration script executed successfully
if [ $? -eq 0 ]; then
    echo "LiteLLM migration script ran successfully!"
else
    echo "LiteLLM migration script failed!"
    exit 1
fi

# Run the Analytics migration script as module
# echo "Running Analytics migrations..."
# python3 -m llm_router.analytics_migration

# Check if the Analytics migration script executed successfully
# if [ $? -eq 0 ]; then
#     echo "Analytics migration script ran successfully!"
# else
#     echo "Analytics migration script failed!"
#     # Не завершаем с ошибкой, так как Analytics не критична для основной функциональности
#     echo "Warning: Analytics migrations failed, but continuing startup..."
# fi

echo "Starting LiteLLM with custom ML router..."
exec "$@"
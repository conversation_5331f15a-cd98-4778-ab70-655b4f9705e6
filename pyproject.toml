[tool.poetry]
name = "litellm"
version = "1.74.14"
description = "Library to easily interface with LLM API providers"
authors = ["BerriAI"]
license = "MIT"
readme = "README.md"
packages = [
    { include = "litellm" },
    { include = "litellm/py.typed"},
]

[tool.poetry.urls]
homepage = "https://litellm.ai"
Homepage = "https://litellm.ai"
repository = "https://github.com/BerriAI/litellm"
Repository = "https://github.com/BerriAI/litellm"
documentation = "https://docs.litellm.ai"
Documentation = "https://docs.litellm.ai"

[tool.poetry.dependencies]
python = ">=3.8.1,<4.0, !=3.9.7"
httpx = ">=0.23.0"
openai = ">=1.68.2"
python-dotenv = ">=0.2.0"
tiktoken = ">=0.7.0"
importlib-metadata = ">=6.8.0"
tokenizers = "*"
click = "*"
jinja2 = "^3.1.2"
aiohttp = ">=3.10"
pydantic = "^2.5.0"
jsonschema = "^4.22.0"
numpydoc = {version = "*", optional = true} # used in utils.py

uvicorn = {version = "^0.29.0", optional = true}
uvloop = {version = "^0.21.0", optional = true, markers="sys_platform != 'win32'"}
gunicorn = {version = "^23.0.0", optional = true}
fastapi = {version = "^0.115.5", optional = true}
backoff = {version = "*", optional = true}
pyyaml = {version = "^6.0.1", optional = true}
rq = {version = "*", optional = true}
orjson = {version = "^3.9.7", optional = true}
apscheduler = {version = "^3.10.4", optional = true}
fastapi-sso = { version = "^0.16.0", optional = true }
PyJWT = { version = "^2.8.0", optional = true }
python-multipart = { version = "^0.0.18", optional = true}
cryptography = {version = "^43.0.1", optional = true}
prisma = {version = "0.11.0", optional = true}
azure-identity = {version = "^1.15.0", optional = true}
azure-keyvault-secrets = {version = "^4.8.0", optional = true}
azure-storage-blob = {version="^12.25.1", optional=true}
google-cloud-kms = {version = "^2.21.3", optional = true}
resend = {version = "^0.8.0", optional = true}
pynacl = {version = "^1.5.0", optional = true}
websockets = {version = "^13.1.0", optional = true}
boto3 = {version = "1.34.34", optional = true}
redisvl = {version = "^0.4.1", optional = true, markers = "python_version >= '3.9' and python_version < '3.14'"}
mcp = {version = "^1.10.0", optional = true, python = ">=3.10"}
litellm-proxy-extras = {version = "0.2.14", optional = true}
rich = {version = "13.7.1", optional = true}
litellm-enterprise = {version = "0.1.16", optional = true}
diskcache = {version = "^5.6.1", optional = true}
polars = {version = "^1.31.0", optional = true, python = ">=3.10"}
semantic-router = {version = "*", optional = true, python = ">=3.9"}
mlflow = {version = ">3.1.4", optional = true, python = ">=3.10"}

[tool.poetry.extras]
proxy = [
    "gunicorn",
    "uvicorn",
    "uvloop",
    "fastapi",
    "backoff",
    "pyyaml",
    "rq",
    "orjson",
    "apscheduler",
    "fastapi-sso",
    "PyJWT",
    "python-multipart",
    "cryptography",
    "pynacl",
    "websockets",
    "boto3",
    "azure-identity",
    "azure-storage-blob",
    "mcp",
    "litellm-proxy-extras",
    "litellm-enterprise",
    "rich",
    "polars",
]

extra_proxy = [
    "prisma",
    "azure-identity",
    "azure-keyvault-secrets",
    "google-cloud-kms",
    "resend",
    "redisvl"
]

utils = [
    "numpydoc",
]

caching = ["diskcache"]

semantic-router = ["semantic-router"]

mlflow = ["mlflow"]

[tool.isort]
profile = "black"

[tool.poetry.scripts]
litellm = 'litellm:run_server'
litellm-proxy = 'litellm.proxy.client.cli:cli'

[tool.poetry.group.dev.dependencies]
flake8 = "^6.1.0"
black = "^23.12.0"
mypy = "^1.0"
pytest = "^7.4.3"
pytest-mock = "^3.12.0"
pytest-asyncio = "^0.21.1"
requests-mock = "^1.12.1"
responses = "^0.25.7"
respx = "^0.22.0"
ruff = "^0.1.0"
types-requests = "*"
types-setuptools = "*"
types-redis = "*"
types-PyYAML = "*"
opentelemetry-api = "1.25.0"
opentelemetry-sdk = "1.25.0"
opentelemetry-exporter-otlp = "1.25.0"
langfuse = "^2.45.0"

[tool.poetry.group.proxy-dev.dependencies]
prisma = "0.11.0"
hypercorn = "^0.15.0"
prometheus-client = "0.20.0"
opentelemetry-api = "1.25.0"
opentelemetry-sdk = "1.25.0"
opentelemetry-exporter-otlp = "1.25.0"
azure-identity = "^1.15.0"

[build-system]
requires = ["poetry-core", "wheel"]
build-backend = "poetry.core.masonry.api"

[tool.commitizen]
version = "1.74.14"
version_files = [
    "pyproject.toml:^version"
]

[tool.mypy]
plugins = "pydantic.mypy"
